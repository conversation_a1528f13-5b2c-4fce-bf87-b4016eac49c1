package com.mall.project.task.executor;

import com.mall.project.dao.quantifyEvolve.QuantifyEvolveDao;
import com.mall.project.service.adminCreditEvolve.AdminCreditEvolveService;
import com.mall.project.service.adminQuantifyEvolve.AdminQuantifyEvolveService;
import com.mall.project.service.adminQuantifyValue.AdminQuantifyValueService;
import com.mall.project.service.areaAuthorize.AreaAuthorizeService;
import com.mall.project.service.cooperateEnterprise.CooperateEnterpriseService;
import com.mall.project.service.creditEvolve.CreditEvolveService;
import com.mall.project.service.dailyTradePercentage.DailyTradePercentageService;
import com.mall.project.service.functionDatas.FunctionDatasService;
import com.mall.project.service.mallBUsers.GetMallBUsersService;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.quantifyEvolve.QuantifyEvolveService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import com.mall.project.service.statusSet.StatusSetService;
import com.mall.project.service.sysDataStatistics.SysDataStatisticsService;
import com.mall.project.service.systemDatas.SystemDatasService;
import com.mall.project.service.systemInfo.SystemInfoService;
import com.mall.project.service.writeOffData.WriteOffDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 数据同步任务执行器
 * 负责执行各种数据同步相关的定时任务
 */
@Slf4j
@Component
public class DataSyncTaskExecutor {
    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");

    @Autowired
    private GetMallBUsersService getMallBUsersService;

    @Autowired
    private CooperateEnterpriseService cooperateEnterpriseService;

    @Autowired
    private DailyTradePercentageService dailyTradePercentageService;

    @Autowired
    private QuantifyCountService quantifyCountService;

    @Autowired
    private AreaAuthorizeService areaAuthorizeService;

    @Autowired
    private FunctionDatasService functionDatasService;

    @Autowired
    private SystemInfoService systemInfoService;

    @Autowired
    private SystemDatasService systemDatasService;

    @Autowired
    private QuantizationValueService quantizationValueService;

    @Autowired
    private QuantifyEvolveDao quantifyEvolveDao;

    @Autowired
    private CreditEvolveService creditEvolveService;

    @Autowired
    private WriteOffDataService writeOffDataService;

    @Autowired
    private SysDataStatisticsService sysDataStatisticsService;

    @Autowired
    private QuantifyEvolveService quantifyEvolveService;

    @Autowired
    private AdminQuantifyValueService adminQuantifyValueService;

    @Autowired
    private AdminQuantifyEvolveService adminQuantifyEvolveService;

    @Autowired
    private AdminCreditEvolveService adminCreditEvolveService;

    @Autowired
    private StatusSetService statusSetService;

    /**
     * 执行数据同步任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeDataSyncTasks() {
        String yesterdayDate = java.time.LocalDate.now().minusDays(1).toString();
        log.info("开始执行数据同步任务");

        try {
            log.info("正在同步用户数据...");
            getMallBUsersService.getMallBUsers();

            log.info("正在同步交易数据...");
            cooperateEnterpriseService.getTradeDataFromMallB();

            log.info("正在从mallB系统读取核销数据...");
            writeOffDataService.getWriteOffDataFromMallB();

            log.info("正在从mallB系统获取用户类型为B的量化值进化量数据...");
            quantifyEvolveService.getQuantifyEvolveFromMallB();

            log.info("正在从mallB系统获取信用值进化量数据...");
            creditEvolveService.getCreditEvolveFromMallB();

            log.info("正在从mallB系统获取 Admin量化值进化量数据...");
            adminQuantifyEvolveService.getAdminQuantifyEvolveFromMallB();

            log.info("正在从mallB系统获取 Admin量化进化量数据...");
            adminCreditEvolveService.getAdminCreditEvolveFromMallB();

            log.info("正在从mallB系统读取 促销金数据...");
            writeOffDataService.getPromotionDataFromMallB();

            log.info("正在从mallB系统读取 核销金数据...");
            writeOffDataService.getWriteOffGoldFromMallB();

            log.info("正在更新企业交易数据...");
            sysDataStatisticsService.statisticsData("query", "0", null);

            log.info("正在更新企业量化数据...");
            sysDataStatisticsService.statisticsData("query", "1", null);

            log.info("正在更新量化率...");
            String currentMonth = LocalDate.now().format(MONTH_FORMAT);
            cooperateEnterpriseService.quantizationRate(currentMonth);

            log.info("正在更新企业交易数据...");
            cooperateEnterpriseService.updateEnterpriseTradeData();

            log.info("正在更新每日交易百分比...");
            dailyTradePercentageService.getDailyTradePercentage();

            log.info("正在更新量化数计算...");
            quantifyCountService.updateQuantifyCount();

            log.info("正在更新C用户补贴金...");
            areaAuthorizeService.updateCSubsidy();

            log.info("正在更新功能数据量化数...");
            functionDatasService.updateQuantifyCount();

            log.info("正在更新系统信息...");
            systemInfoService.updateSystemInfo();

            log.info("正在更新系统数据...");
            systemDatasService.updateSystemDatas(yesterdayDate);

            log.info("正在更新量化值...");
            quantizationValueService.updateQuantizationValue();

            log.info("正在更新量化进化量...");
            quantifyEvolveDao.updateQuantifyEvolve();

            log.info("正在更新信用值进化量...");
            creditEvolveService.updateCreditEvolve();

            log.info("正在执行每日Admin量化值计算...");
            adminQuantifyValueService.updateAdminQuantifyValue(yesterdayDate);

            log.info("正在计算 用户状态为不正常的用户, 当日的量化值,补贴金统计...");
            statusSetService.updateQuantifyAndSubsidy();

            log.info("数据同步任务执行完成");

        } catch (Exception e) {
            log.error("数据同步任务执行过程中发生错误", e);
            throw e;
        }
    }
}
