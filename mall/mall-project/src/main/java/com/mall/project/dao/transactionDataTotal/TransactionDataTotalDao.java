package com.mall.project.dao.transactionDataTotal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class TransactionDataTotalDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 获取累计交易金额
    public String sumTradeAmountTotal(String phone, String startDate) {
        List<Object> params = new ArrayList<>();
        // 构建查询语句
        String sql = "SELECT COALESCE(sum(trade_amount), '0') as trade_amount_total " +
                     "FROM enterprise_product_data WHERE status = '0'";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_time) <= ?";
            params.add(startDate);
        } else {
            sql += " AND DATE(update_time) <= CURDATE() - INTERVAL 1 DAY";
        }
        try {
            String result;
            if (params.isEmpty()) {
                result = jdbcTemplate.queryForObject(sql, String.class);
            } else {
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }

    // 查询统计累计交易数据导出
    public List<Map<String, Object>> transactionDataTotalExport(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT DATE(p.update_time) as update_time, p.phone, p.company_name, p.trade_name, " +
                     "sum(total_count) as trade_amount " +
                     "FROM enterprise_product_data p " +
                     "WHERE p.trade_name IS NOT NULL AND p.trade_amount IS NOT NULL";

        if (phone != null && !phone.isEmpty()) {
            sql += " AND p.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(p.update_time) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(p.update_time) <= CURDATE() ";
        }

        sql += " GROUP BY DATE(p.update_time), p.phone, p.company_name, p.trade_name" +
               " ORDER BY DATE(p.update_time) DESC";

        return jdbcTemplate.queryForList(sql, params.toArray());
    }
    // 查询统计累计交易数据 分页显示
    public List<Map<String, Object>> QuerytransactionDataTotalPages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        // 构建查询语句
        String sql = "SELECT DATE(p.update_time) as update_time, p.phone, p.company_name, p.trade_name, " +
                     "sum(total_count) as trade_amount " +
                     "FROM enterprise_product_data p " +
                     "WHERE p.trade_name IS NOT NULL AND p.trade_amount IS NOT NULL";

        if (phone != null && !phone.isEmpty()) {
            sql += " AND p.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(p.update_time) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(p.update_time) <= CURDATE() ";
        }
        sql += " GROUP BY DATE(p.update_time), p.phone, p.company_name, p.trade_name" +
               " ORDER BY DATE(p.update_time) DESC LIMIT ? OFFSET ?";

        params.add(limit);
        params.add(offset);

        return jdbcTemplate.queryForList(sql, params.toArray());
    }
    // 查询统计累计交易数据 总条数
    public int countTransactionDataTotal(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        // 构建查询语句 - 需要与分页查询保持一致，使用GROUP BY后的计数
        String sql = "select count(1) from enterprise_product_data WHERE trade_name IS NOT NULL AND trade_amount IS NOT NULL";

        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_time) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_time) <= CURDATE() ";
        }
        try {
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        } catch (Exception e) {
            return 0;
        }
    }
}
